# 系统缓存清理脚本
# 作者：AI助手
# 创建时间：2025-08-05

Write-Host "=== 系统缓存清理工具 ===" -ForegroundColor Green
Write-Host "开始清理系统缓存..." -ForegroundColor Yellow

# 1. 清理conda缓存
Write-Host "`n[1/6] 清理Conda缓存..." -ForegroundColor Cyan
try {
    conda clean --all --yes
    Write-Host "✅ Conda缓存清理完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Conda缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 清理pip缓存
Write-Host "`n[2/6] 清理Pip缓存..." -ForegroundColor Cyan
try {
    pip cache purge
    Write-Host "✅ Pip缓存清理完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Pip缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 清理npm缓存
Write-Host "`n[3/6] 清理NPM缓存..." -ForegroundColor Cyan
try {
    npm cache clean --force
    Write-Host "✅ NPM缓存清理完成" -ForegroundColor Green
} catch {
    Write-Host "❌ NPM缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 清理Chrome缓存
Write-Host "`n[4/6] 清理Chrome缓存..." -ForegroundColor Cyan
$chromeCachePath = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Cache"
if (Test-Path $chromeCachePath) {
    try {
        Remove-Item "$chromeCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Chrome缓存清理完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ Chrome缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️ Chrome缓存目录不存在" -ForegroundColor Yellow
}

# 5. 清理D3D缓存
Write-Host "`n[5/6] 清理D3D着色器缓存..." -ForegroundColor Cyan
$d3dCachePath = "$env:LOCALAPPDATA\D3DSCache"
if (Test-Path $d3dCachePath) {
    try {
        Remove-Item "$d3dCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ D3D缓存清理完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ D3D缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️ D3D缓存目录不存在" -ForegroundColor Yellow
}

# 6. 清理临时文件
Write-Host "`n[6/6] 清理临时文件..." -ForegroundColor Cyan
$tempPath = "$env:LOCALAPPDATA\Temp"
if (Test-Path $tempPath) {
    try {
        Remove-Item "$tempPath\*" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ 临时文件清理完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 临时文件清理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️ 临时文件目录不存在" -ForegroundColor Yellow
}

# 显示磁盘空间信息
Write-Host "`n=== 磁盘空间信息 ===" -ForegroundColor Green
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}, @{Name="Used(GB)";Expression={[math]::Round(($_.Size-$_.FreeSpace)/1GB,2)}} | Format-Table

Write-Host "`n🎉 缓存清理完成！" -ForegroundColor Green
Write-Host "建议每周运行一次此脚本以保持系统清洁。" -ForegroundColor Yellow

# 暂停以查看结果
Read-Host "`n按Enter键退出..."
